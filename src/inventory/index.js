const db = require('../../config/database');

exports.handler = async (event, context) => {
  try {
    // Parse input JSON from the event body
    const inputArray = JSON.parse(event.body);

    // Validate that inputArray is an array
    if (!Array.isArray(inputArray)) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Invalid input. Expected an array of objects.' }),
      };
    }

    // Process each object in the array
    const results = await Promise.all(inputArray.map(processInputObject));

    // Respond with success or other appropriate logic
    return {
      statusCode: 200,
      body: JSON.stringify(results),
    };
  } catch (error) {
    // Handle database or other errors
    console.error(error);
    // Respond with an error if necessary
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal Server Error' }),
    };
  }
};

// Function to execute a database query for a single input object
async function processInputObject(inputObject) {
  try {
    // Extract properties from the input object
    const { product_id, business_id, branch_id, inventory_limit } = inputObject;

    // Validate required properties
    if (!product_id || !business_id || !branch_id || isNaN(inventory_limit)) {
      console.error(`Invalid input object: ${JSON.stringify(inputObject)}`);
      return {
        error: `Invalid input object. Missing required properties or invalid inventory_limit for ${product_id}, ${business_id}, ${branch_id}.`,
      };
    }

    // Fetch the existing location_inventory JSON from the database
    const existingJsonResult = await executeQuery(`SELECT location_inventory FROM menu WHERE mid = ${product_id} AND rid = ${business_id}`);
    let existingJson;

    if (existingJsonResult[0].location_inventory === null) {
      // If location_inventory is null, initialize existingJson as a new object
      existingJson = { [branch_id]: inventory_limit };
    } else {
      // If location_inventory is not null, parse the existing JSON
      existingJson = JSON.parse(existingJsonResult[0].location_inventory);
      // Update the existing JSON or add a new entry
      existingJson[branch_id] = inventory_limit;
    }

    // Convert updated JSON object to a string before saving to the database
    const jsonString = JSON.stringify(existingJson);

    // Update the database with the updated JSON data
    await executeQuery(`UPDATE menu SET location_inventory = '${jsonString}' WHERE mid = ${product_id} AND rid = ${business_id}`);

    return {
      success: `Successfully processed input object for ${product_id}, ${business_id}, ${branch_id}.`,
    };
  } catch (error) {
    // Handle errors for a specific input object
    console.error(`Error processing input object: ${JSON.stringify(inputObject)}`, error);
    return {
      error: `Error processing input object for ${inputObject.product_id}, ${inputObject.business_id}, ${inputObject.branch_id}.`,
    };
  }
}

// Function to execute a database query
function executeQuery(sqlQuery, mappedFields = []) {
  // Use db.queryPromise with db.pool
  return db.queryPromise(db.pool, sqlQuery, mappedFields);
}
