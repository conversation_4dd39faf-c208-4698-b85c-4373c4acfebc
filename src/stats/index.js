const connection = require('../../config/database');
const businessModal = require("../../models/businessModel");

// Set default headers
const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*", // or specify your allowed origin
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};
exports.getMetrics = async (event, context) => {
    const businessId = event.pathParameters.business_id;
    const { startDate, endDate, countryCode, areas, categories, products, branchWiseSummary } = event.queryStringParameters;

    let time_zone = '+05:00';
    let currencyCode = 'PKR';
    let businessSettings = {};
    if(businessId != 'all') {
        businessSettings = await businessModal.getBusinessDetails(businessId);
        time_zone = businessSettings.time_zone;
        currencyCode = businessSettings.currencycode;
    }
    if (countryCode === 'CA') {
        time_zone = '-05:00';
        currencyCode = 'CAD';
    }
   function getOrdersTable(startDate, endDate) {
    const marchDate = new Date('2025-03-01T00:00:00'); // Safe ISO format
    const start = new Date(startDate.replace(' ', 'T')); // Convert to ISO
    const end = new Date(endDate.replace(' ', 'T'));

    if (start < marchDate) {
        console.log("hiii");
        return 'orders_archive';
    }

    return 'orders';
   }


    const ordersTable = getOrdersTable(startDate, endDate);
    console.log(ordersTable);
    const queries = {
        totalSales: `
            SELECT IFNULL(SUM(o.gtotal), 0) AS totalSales 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencyCode = ? AND 
                  o.status NOT IN ('Cancel', 'Viewed', 'Pending') AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        totalOrders: `
            SELECT IFNULL(COUNT(*), 0) AS totalOrders 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencycode = ? AND 
                  o.status NOT IN ('Cancel', 'Viewed', 'Pending') AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        totalCustomers: `
            SELECT IFNULL(COUNT(DISTINCT o.email), 0) AS totalCustomers 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencycode = ? AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        averageOrderValue: `
            SELECT IFNULL(AVG(o.gtotal), 0) AS aov 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencycode = ? AND 
                  o.status NOT IN ('Cancel', 'Viewed', 'Pending') AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        pendingSales: `
            SELECT IFNULL(SUM(o.gtotal), 0) AS pendingSales 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencycode = ? AND 
                  o.status = "Pending" AND 
                  o.payment_type != 3 AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        pendingOrders: `
            SELECT IFNULL(COUNT(*), 0) AS pendingOrders 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencycode = ? AND 
                  o.status = "Pending" AND 
                  o.payment_type != 3 AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        cancelledSales: `
            SELECT IFNULL(SUM(o.gtotal), 0) AS cancelledSales 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencycode = ? AND 
                  o.status = "Cancel" AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        cancelledOrders: `
            SELECT IFNULL(COUNT(*), 0) AS cancelledOrders 
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencycode = ? AND 
                  o.status = "Cancel" AND 
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
        `,
        dailyMetrics: `SELECT 
            DATE(CONVERT_TZ(o.date, @@session.time_zone, ?)) AS day, 
            IFNULL(SUM(CASE WHEN o.status NOT IN ('Cancel', 'Viewed', 'Pending') THEN o.gtotal ELSE 0 END), 0) AS dailySales, 
            IFNULL(COUNT(CASE WHEN o.status NOT IN ('Cancel', 'Viewed', 'Pending') THEN 1 ELSE NULL END), 0) AS dailyOrders,
            IFNULL(COUNT(DISTINCT o.email), 0) AS totalCustomers,
            IFNULL(AVG(CASE WHEN o.status NOT IN ('Cancel', 'Viewed', 'Pending') THEN o.gtotal ELSE NULL END), 0) AS dailyAOV,
            IFNULL(SUM(CASE WHEN o.status = 'Pending' AND o.payment_type != 3 THEN o.gtotal ELSE 0 END), 0) AS dailyPendingSales,
            IFNULL(COUNT(CASE WHEN o.status = 'Pending' AND o.payment_type != 3 THEN 1 ELSE NULL END), 0) AS dailyPendingOrders,
            IFNULL(SUM(CASE WHEN o.status = 'Cancel' THEN o.gtotal ELSE 0 END), 0) AS dailyCancelledSales,
            IFNULL(COUNT(CASE WHEN o.status = 'Cancel' THEN 1 ELSE NULL END), 0) AS dailyCancelledOrders
        FROM 
            ${ordersTable} o
        INNER JOIN 
            restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''} 
            r.currencycode = ? AND
            CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ? 
        GROUP BY day
        ORDER BY day;
        `,
        dailyMetricsBySource: `SELECT DATE(CONVERT_TZ(o.date, @@session.time_zone, ?)) AS day, 
        o.source,
            IFNULL(SUM(CASE WHEN o.status NOT IN ('Cancel', 'Viewed', 'Pending') THEN o.gtotal ELSE 0 END), 0) AS dailySales, 
            IFNULL(COUNT(CASE WHEN o.status NOT IN ('Cancel', 'Viewed', 'Pending') THEN 1 ELSE NULL END), 0) AS dailyOrders,
            IFNULL(COUNT(DISTINCT o.email), 0) AS totalCustomers,
            IFNULL(AVG(CASE WHEN o.status NOT IN ('Cancel', 'Viewed', 'Pending') THEN o.gtotal ELSE NULL END), 0) AS dailyAOV,
            IFNULL(SUM(CASE WHEN o.status = 'Pending' AND o.payment_type != 3 THEN o.gtotal ELSE 0 END), 0) AS dailyPendingSales,
            IFNULL(COUNT(CASE WHEN o.status = 'Pending' AND o.payment_type != 3 THEN 1 ELSE NULL END), 0) AS dailyPendingOrders,
            IFNULL(SUM(CASE WHEN o.status = 'Cancel' THEN o.gtotal ELSE 0 END), 0) AS dailyCancelledSales,
            IFNULL(COUNT(CASE WHEN o.status = 'Cancel' THEN 1 ELSE NULL END), 0) AS dailyCancelledOrders
            FROM ${ordersTable} o
            INNER JOIN 
                restaurants r ON o.r_id = r.r_id
                WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                r.currencyCode = ? AND
                CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ? 
            GROUP BY day, o.source 
            ORDER BY day, o.source`,
        heatMap: `
        SELECT DATE(CONVERT_TZ(o.date, @@session.time_zone, ?)) AS day, 
               o.user_latitude, 
               o.user_longitude 
        FROM ${ordersTable} o
        INNER JOIN restaurants r ON o.r_id = r.r_id
        WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
              r.currencyCode = ? AND 
              o.status NOT IN ('Cancel', 'Viewed', 'Pending') AND 
              CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ? AND 
              o.user_latitude IS NOT NULL AND 
              o.user_longitude IS NOT NULL AND 
              o.user_latitude != '' AND 
              o.user_longitude != ''
     `,
    };
    if (categories === 'true') {
        queries.topCategories = `
            SELECT 
                mc.menu_cat_id AS categoryId,
                mc.catname AS categoryName, 
                COUNT(DISTINCT m.mid) AS totalProducts, 
                SUM(od.dtotal) AS totalRevenue
            FROM orderdetails od
            JOIN menu m ON m.mid = od.menu_item_id
            JOIN menucats mc ON mc.menu_cat_id = m.menu_cat_id
            JOIN ${ordersTable} o ON o.orderid = od.orderid
            WHERE o.status = 'Confirmed'
            AND o.r_id = ?
            AND CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
            GROUP BY mc.menu_cat_id
            ORDER BY totalRevenue DESC;
        `;
    }
    
    if (products === 'true') {
        queries.topProducts = `
            SELECT 
                m.mid AS id,
                m.product_code AS productCode, 
                m.dname AS menuItems, 
                c.catname AS categoryName,
                SUM(od.dqty) AS count, 
                SUM(od.dtotal) AS subTotal,
                SUM(od.tax) AS tax,
                SUM(od.discount) AS discount,
                SUM(od.coupon_discount) AS couponDiscount,
                SUM(od.dtotal + od.tax - od.discount - od.coupon_discount) AS grandTotal
            FROM orderdetails od
            JOIN menu m ON m.mid = od.menu_item_id
            JOIN menucats c ON c.menu_cat_id = m.menu_cat_id
            JOIN ${ordersTable} o ON o.orderid = od.orderid
            WHERE o.status = 'Confirmed'
            AND o.r_id = ?
            AND CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
            GROUP BY m.mid, c.menu_cat_id
            ORDER BY subTotal DESC;
        `;
    }
    
    if (branchWiseSummary === 'true') {
        queries.branchWiseSummary = `
           SELECT 
                b.id AS id,
                b.location AS branch,
                COUNT(o.orderid) AS orders,
                IFNULL(SUM(o.total), 0) AS subTotal,
                IFNULL(SUM(o.tax_value), 0) AS tax,
                IFNULL(SUM(o.discount_value), 0) AS discount,
                IFNULL(SUM(o.custom_code_discount_value), 0) AS couponDiscount,
                IFNULL(SUM(o.delivery_charges), 0) AS deliveryCharges,
                IFNULL(SUM(o.delivery_tax_value), 0) AS deliveryTax,
                IFNULL(SUM(o.tip), 0) AS tip,
                IFNULL(SUM(o.service_charges), 0) AS serviceCharges,
                IFNULL(SUM(o.gtotal), 0) AS grandTotal
            FROM ${ordersTable} o
            INNER JOIN addresses b ON o.bid = b.id
            WHERE o.status NOT IN ('Cancel', 'Viewed', 'Pending') -- Added 'Pending' here for consistency
            AND o.r_id = ?
            AND CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ?
            GROUP BY b.location
            ORDER BY grandTotal DESC;
        `;
    }
    
    const metrics = {};

    const runQueries = async (query, key, params) => {
        try {
            console.log(`Running query for ${key}`);
            const startTime = Date.now();

            const results = await connection.queryPromise(connection.pool, query, params); // Await the query execution
            const endTime = Date.now();
            console.log(`Query for ${key} took ${endTime - startTime}ms`);

            metrics[key] = results; // Save the entire result set
        } catch (err) {
            console.error(`Error running query for ${key}: `, err);
        }
    };

    try {
        await Promise.all(Object.keys(queries).map(key => {
            const query = queries[key];
            let params;
            if (key === 'dailyMetrics' || key === 'dailyMetricsBySource' || key === 'heatMap') {
                params = businessId === 'all'
                    ? [time_zone, currencyCode, time_zone, startDate, endDate, time_zone]
                    : [time_zone, businessId, currencyCode, time_zone, startDate, endDate];
            } else if (key === 'topCategories' || key === 'topProducts' || key === 'branchWiseSummary') {
                params = businessId === 'all'
                        ? [time_zone, startDate, endDate]
                        : [businessId, time_zone, startDate, endDate];
            } else {
                params = businessId === 'all'
                    ? [currencyCode, time_zone, startDate, endDate]
                    : [businessId, currencyCode, time_zone, startDate, endDate];
            }
            return runQueries(query, key, params);
        }));

       
        // Format daily metrics into an array of objects if it exists
        const dailyMetrics = metrics.dailyMetrics ? metrics.dailyMetrics.map(row => {
            const cleanedRow = {
                date: row.day,
                totalOrders: row.dailyOrders || 0,
                totalSales: row.dailySales || 0,
                totalCustomers: row.totalCustomers || 0,
                averageOrderValue: row.dailyAOV || 0,
                pendingOrders: row.dailyPendingOrders || 0,
                pendingSales: row.dailyPendingSales || 0,
                cancelledOrders: row.dailyCancelledOrders || 0,
                cancelledSales: row.dailyCancelledSales || 0,
            };
            return Object.keys(cleanedRow).length > 1 ? cleanedRow : null; // Return null if only the date remains
        }).filter(row => row !== null) : [];

        const heatMap = metrics.heatMap ? metrics.heatMap.map(row => ({
            userLat: parseFloat(row.user_latitude),
            userLng: parseFloat(row.user_longitude),
        })) : [];

        // Group dailyMetricsBySource by date and encapsulate source data
        const dailyMetricsBySource = metrics.dailyMetricsBySource ? Object.values(metrics.dailyMetricsBySource.reduce((acc, row) => {

            if (!acc[row.day]) {
                acc[row.day] = {
                    date: row.day,
                    sources: []
                };
            }
            
            const cleanedSourceData = {
                source: row.source,
                totalOrders: row.dailyOrders || 0,
                totalSales: row.dailySales || 0,
                totalCustomers: row.totalCustomers || 0,
                averageOrderValue: row.dailyAOV || 0,
                pendingOrders: row.dailyPendingOrders || 0,
                pendingSales: row.dailyPendingSales || 0,
                cancelledOrders: row.dailyCancelledOrders || 0,
                cancelledSales: row.dailyCancelledSales || 0
            };

            if (Object.keys(cleanedSourceData).length > 1) {
                acc[row.day].sources.push(cleanedSourceData);
            }

            return acc;
        }, {})) : [];
        if (areas === 'true') {
            console.log('Fetching top areas...');
            const topAreas = await getTopAreas(businessId, startDate, endDate, currencyCode, time_zone, ordersTable);
            metrics.topAreas = topAreas; // Add top areas to the metrics
        }    
        console.log(metrics.topAreas);
        // Construct the final response
        const response = {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                currency: businessSettings.currencycode,
                totalSales: metrics.totalSales?.[0]?.totalSales || 0,
                totalOrders: metrics.totalOrders?.[0]?.totalOrders || 0,
                totalCustomers: metrics.totalCustomers?.[0]?.totalCustomers || 0,
                averageOrderValue: metrics.averageOrderValue?.[0]?.aov || 0,
                pendingSales: metrics.pendingSales?.[0]?.pendingSales || 0,
                pendingOrders: metrics.pendingOrders?.[0]?.pendingOrders || 0,
                cancelledSales: metrics.cancelledSales?.[0]?.cancelledSales || 0,
                cancelledOrders: metrics.cancelledOrders?.[0]?.cancelledOrders || 0,
                dailyMetrics: dailyMetrics || [],
                dailyMetricsBySource: dailyMetricsBySource || [],
                heatMap: heatMap || [],
                topAreas: metrics.topAreas || [],
                topCategories: (categories === 'true' ? metrics.topCategories  : []),
                topProducts : (products === 'true' ? metrics.topProducts : []),
                branchWiseSummary : (branchWiseSummary === 'true' ? metrics.branchWiseSummary : []),
            }),
        };        
        return response;
    } catch (err) {
        console.error('Error in getMetrics:', err);
        return {
            headers,
            statusCode: 500,
            body: JSON.stringify({ error: err.message }),
        };
    }
};
async function getTopAreas(businessId, startDate, endDate, currencyCode, time_zone, ordersTable) {
    return new Promise((resolve, reject) => {
        const query = `
            SELECT COUNT(*) as ordersCount, 
                   o.area,
                   IFNULL(SUM(o.gtotal), 0) AS totalSales
            FROM ${ordersTable} o
            INNER JOIN restaurants r ON o.r_id = r.r_id
            WHERE ${businessId !== 'all' ? 'o.r_id = ? AND' : ''}
                  r.currencyCode = ? AND
                  CONVERT_TZ(o.date, @@session.time_zone, ?) BETWEEN ? AND ? AND
                  o.area IS NOT NULL AND
                  o.order_type = 'delivery'
            GROUP BY o.area
            ORDER BY ordersCount DESC;
        `;
        const params = businessId === 'all'
            ? [currencyCode, time_zone, startDate, endDate]
            : [businessId, currencyCode, time_zone, startDate, endDate];

            connection.queryPromise(connection.pool, query, params)
            .then(resolve)
            .catch(reject);
            });
}
