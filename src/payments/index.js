const response = require("../../uitls/response");
const bankAlfalahHelper = require("../../uitls/payments/bankAlfalahHelper");
const payfastHelper = require("../../uitls/payments/payfastHelper");
const paymobHelper = require("../../uitls/payments/paymobHelper");
const stripeHelper = require("../../uitls/payments/stripeHelper");
const orderValidator = require("../../uitls/jsonValidator");
const jwtAuthenticator = require("../../uitls/jwtAuthenticator");

// Set default headers
const headers = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*", // or specify your allowed origin
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept",
  "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};

module.exports.registerStripeCustomer = async (event) => {
  try {
    const businessId = event.pathParameters.business_id;
    const paymentType = event.pathParameters.pay_type;
    const requestBody = JSON.parse(event.body);
    const requestHeaders = event.headers;

    const validateJson =
      orderValidator.validateRegisterStripeCustomerJson(requestBody);

    switch (paymentType) {
      case "stripe":
        // Add handling for Stripe payment here if needed
        const stripeResponse = await stripeHelper.registerCustomer(
          businessId,
          validateJson,
          requestHeaders
        );
        return response.createResponse(
          stripeResponse.status,
          headers,
          stripeResponse.message,
          stripeResponse.response
        );
        break;
      default:
        // Handle other payment types or throw an error for unsupported types
        return response.createResponse(400, headers, "Bad Request", {});
        break;
    }
  } catch (error) {
    // Handle errors
    console.error("Error fetching payment details:", error);
    return response.createResponse(400, headers, "Bad Request", error.message);
  }
};

module.exports.chargeOnlinePayment = async (event) => {
  try {
    // Extract headers
    const requestHeaders = event.headers || {};
    const userAgent =
      requestHeaders["User-Agent"] || requestHeaders["user-agent"] || "";
    // const referer = headers["Referer"] || headers["referer"] || "";
    // const apiKey = headers["x-api-key"] || "";

    // Block requests from Postman, cURL, and bots
    if (
      userAgent.includes("PostmanRuntime") ||
      userAgent.includes("curl") ||
      userAgent.includes("python-requests")
    ) {
      throw new Error("Unauthorized request: API access denied.");
    }
    const businessId = event.pathParameters.business_id;
    const paymentType = event.pathParameters.pay_type;
    const requestBody = JSON.parse(event.body);
    // const requestHeaders = event.headers;

    const validateJson = orderValidator.validatePaymentJson(requestBody);

    switch (paymentType) {
      case "bank_alfalah":
        const baflResponse = await bankAlfalahHelper.chargeBankPayment(
          businessId,
          validateJson
        );
        return response.createResponse(
          baflResponse.status,
          headers,
          "Session created",
          baflResponse.response
        );
        break;
      case "paymob":
        const paymobResponse = await paymobHelper.chargePaymobPayment(
          businessId,
          validateJson
        );
        return response.createResponse(
          paymobResponse.status,
          headers,
          paymobResponse.message,
          paymobResponse.result
        );
        break;
      case "payfast":
        const payfastResponse = await payfastHelper.chargePayFastPayment(
          businessId,
          validateJson
        );
        return response.createResponse(
          payfastResponse.status,
          headers,
          payfastResponse.message,
          payfastResponse.result
        );
        break;
      case "stripe":
        // Add handling for Stripe payment here if needed
        const stripeResponse = await stripeHelper.chargePayment(
          businessId,
          validateJson,
          requestHeaders
        );
        return response.createResponse(
          stripeResponse.status,
          headers,
          stripeResponse.message,
          stripeResponse.response
        );
        break;
      default:
        // Handle other payment types or throw an error for unsupported types
        return response.createResponse(400, headers, "Bad Request", {});
        break;
    }
  } catch (error) {
    // Handle errors
    console.error("Error fetching payment details:", error);
    return response.createResponse(400, headers, "Bad Request", error.message);
  }
};

module.exports.fetchCustomerDetails = async (event) => {
  try {
    const businessId = event.pathParameters.business_id;
    const customerId = event.pathParameters.customer_id;
    const paymentType = event.pathParameters.pay_type;
    const requestBody = JSON.parse(event.body);

    // const validateJson = orderValidator.validatePaymentJson(requestBody);
    const requestHeaders = event.headers;
    if (requestHeaders.Authorization) {
      const tokenStatus = await jwtAuthenticator.verifyToken(
        requestHeaders.Authorization
      );
      if (tokenStatus.valid == false) {
        return response.createResponse(401, headers, tokenStatus.message, {});
      }
      switch (paymentType) {
        case "stripe":
          // Add handling for Stripe payment here if needed
          const stripeResponse = await stripeHelper.getCustomerCardDetails(
            businessId,
            customerId,
            requestBody
          );
          return response.createResponse(
            stripeResponse.status,
            headers,
            stripeResponse.message,
            stripeResponse.response
          );
          break;
        default:
          // Handle other payment types or throw an error for unsupported types
          return response.createResponse(400, headers, "Bad Request", {});
          break;
      }
    } else if (!headers.Authorization) {
      return response.createResponse(
        401,
        headers,
        "UnAuthorized: Access token missing",
        {}
      );
    }
  } catch (error) {
    // Handle errors
    console.error("Error fetching payment details:", error);
    return response.createResponse(400, headers, "Bad Request", error.message);
  }
};

module.exports.addCustomerDetails = async (event) => {
  try {
    const businessId = event.pathParameters.business_id;
    const customerId = event.pathParameters.customer_id;
    const paymentType = event.pathParameters.pay_type;
    const requestBody = JSON.parse(event.body);

    // const validateJson = orderValidator.validatePaymentJson(requestBody);

    switch (paymentType) {
      case "stripe":
        // Add handling for Stripe payment here if needed
        const stripeResponse = await stripeHelper.addCustomerCardDetails(
          businessId,
          customerId,
          requestBody
        );
        return response.createResponse(
          stripeResponse.status,
          headers,
          stripeResponse.message,
          stripeResponse.response
        );
        break;
      default:
        // Handle other payment types or throw an error for unsupported types
        return response.createResponse(400, headers, "Bad Request", {});
        break;
    }
  } catch (error) {
    // Handle errors
    console.error("Error fetching payment details:", error);
    return response.createResponse(400, headers, "Bad Request", error.message);
  }
};

module.exports.webhookOnlinePayment = async (event) => {
  try {
    const paymentType = event.pathParameters.pay_type;
    const requestBody = JSON.parse(event.body);

    switch (paymentType) {
      case "paymob":
        const paymobResponse = await paymobHelper.chargePaymentStatus(
          requestBody
        );
        return response.createResponse(
          paymobResponse.status,
          headers,
          paymobResponse.message,
          paymobResponse.result
        );
        break;
      default:
        // Handle other payment types or throw an error for unsupported types
        return response.createResponse(400, headers, "Bad Request", {});
        break;
    }
  } catch (error) {
    // Handle errors
    console.error("Error fetching payment details:", error);
    return response.createResponse(400, headers, "Bad Request", error);
  }
};
