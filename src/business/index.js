const response = require("../../uitls/response");
const businessModal = require("../../models/businessModel");
const timeSlotHelper = require("../../uitls/timeSlotHelper");
const paymentsHelper = require("../../uitls/integrationHelper");
const { pool, superAdminDB, queryPromise } = require("../../config/database");
const moment = require("moment");

// Set default headers
const headers = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*", // or specify your allowed origin
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept",
  "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};

// Function to get business settings
module.exports.getBusinessSetting = async (event) => {
  // Extract business ID from path parameters

  try {
    const businessId = event.pathParameters.business_id;
    if (businessId == 0 || businessId == "") {
      return response.createResponse(
        400,
        headers,
        "Business id is required",
        {}
      );
    }
    // Fetch business details using the business ID
    let businessSettings = await businessModal.getBusinessDetails(businessId);
    // const paymentSettings = await paymentsHelper.getPaymentMethods(businessId);

    businessSettings.logo =
      "https://static.tossdown.com/logos/" + businessSettings.logo;
    // businessSettings.payment_options = paymentSettings;

    // Return success response
    return response.createResponse(200, headers, "Success", businessSettings);
  } catch (err) {
    // Return error response in case of an exception
    return response.createResponse(400, headers, err, {});
  }
};

// Function to get branch settings
module.exports.getBranchSetting = async (event) => {
  try {
    // Extract business and location IDs from path parameters
    const businessId = event.pathParameters.business_id;
    const locationId = event.pathParameters.location_id || 0;

    // Fetch business details using the business ID
    const businessSettings = await businessModal.getBusinessDetails(businessId);
    // Fetch branch details using business and location IDs
    const branchSettings = await businessModal.getBranchDetails(
      businessId,
      locationId
    );

    const businessSettingsMapped = branchSettings.map((item) => ({
      ...item,
      settings:
        item.settings ||
        JSON.stringify({
          cart: {
            tax: "",
            discount: "",
            tax_type: "",
            minimum_spent: "",
            online_payment_tax: "",
          },
        }), /// If item.settings is null, set it to {}
    }));
    const paymentSettings = await paymentsHelper.getPaymentMethods(
      businessId,
      businessSettings
    );
    const addressAPI = await paymentsHelper.getAddressAPI(businessId);

    let result = businessSettings;
    result.logo = "https://static.tossdown.com/logos/" + businessSettings.logo;
    result.payment_options = paymentSettings;
    result.address_api = addressAPI;
    result.branches = businessSettingsMapped;

    // Return success response
    return response.createResponse(200, headers, "Success", result);
  } catch (err) {
    // Return error response in case of an exception
    return response.createResponse(400, headers, "An error occured", err);
  }
};

// Function to get branch settings
module.exports.getBranchPickupHours = async (event) => {
  try {
    // Extract business and location IDs from path parameters
    const businessId = event.pathParameters.business_id;
    const locationId = event.pathParameters.location_id || 0;
    const utcTime = moment.utc();

    // Fetch business details using the business ID
    const businessSettings = await businessModal.getBusinessDetails(businessId);

    // Fetch branch details using business and location IDs
    const branchSettings = await businessModal.getBranchDetails(
      businessId,
      locationId
    );
    const preparationTime = businessSettings.preparation_time;
    const date = new Date("2022-02-04");
    const singleBranchSettings = branchSettings[0];
    const getSpecificTiming = JSON.parse(singleBranchSettings.timing);

    const pickupTiming = getSpecificTiming.pickup || {};
    day = "1";
    const pickupDayTiming = pickupTiming[day] || [];
    const getSpecificTimeZone = singleBranchSettings.time_zone;

    const convertedTime = utcTime.clone().utcOffset(getSpecificTimeZone);
    const currentTime = convertedTime.format();

    const result = await timeSlotHelper.generateTimeSlots(1, pickupTiming);

    // Return success response
    return response.createResponse(200, headers, "Success", result);
  } catch (err) {
    // Return error response in case of an exception
    return response.createResponse(400, headers, "An error occured", err);
  }
};

// Function to get restaurants with filters
module.exports.getRestaurantsAndBranches = async (event) => {
  try {
    const { name, city, geo_location, cuisine, type, logo, featured_products, limit, offset } =
      event.queryStringParameters || {};

    // Step 1: Fetch restaurants without city filter
    let restaurantQuery = `
            SELECT res_id, name, logo_image, subscription, domain 
            FROM restaurant 
            WHERE domain != "" AND subscription != ""
        `;
    const queryParams = [];

    if (name) {
      restaurantQuery += ` AND LOWER(name) LIKE LOWER(?)`;
      queryParams.push(`%${name}%`);
    }
    if (type) {
      restaurantQuery += ` AND LOWER(JSON_UNQUOTE(JSON_EXTRACT(subscription, '$.theme.label'))) = LOWER(?)`;
      queryParams.push(type);
    }
    if (logo === '1') {
      restaurantQuery += ` AND logo_image IS NOT NULL AND logo_image != ''`;
    }

    // Fetch restaurants
    const restaurants = await queryPromise(
      superAdminDB,
      restaurantQuery,
      queryParams
    );
    if (!Array.isArray(restaurants) || restaurants.length === 0) {
      return {
        statusCode: 404,
        body: JSON.stringify({ message: "No restaurants found" }),
      };
    }


    // Step 2: Add restaurant type
    const restaurantsWithType = restaurants.map((restaurant) => {
      let type = null;
      let businessCategory = null;
      try {
        const subscription = restaurant.subscription;
        const parsedSubscription =
          typeof subscription === "string"
            ? JSON.parse(subscription)
            : subscription;
        businessCategory = parsedSubscription.category;
        type = parsedSubscription?.theme?.label || null;
      } catch (err) {
        console.error("Error parsing subscription JSON: ", err);
      }
      return { ...restaurant, type, businessCategory };
    });
    // Step 3: Fetch branches and apply city filter
    const restaurantIds = restaurantsWithType.map((r) => r.res_id);
    // console.log("Restaurant IDs: ", restaurantIds);

    let branchQuery = `SELECT * FROM addresses WHERE r_id IN (?)`;
    const branchQueryParams = [restaurantIds];
    if (city) {
      branchQuery += ` AND LOWER(city) LIKE LOWER(?)`;
      branchQueryParams.push(`%${city}%`);
    }
    if (geo_location) {
      const [lat, lng] = geo_location.split(",");
      branchQuery += ` AND ST_Distance_Sphere(POINT(lng, lat), POINT(?, ?)) < 5000`;
      branchQueryParams.push(lng, lat);
    }

    const branches = await queryPromise(pool, branchQuery, branchQueryParams);

    // Step 4: Filter out restaurants that don't have a branch in the requested city
    const validRestaurantIds = new Set(branches.map((branch) => branch.r_id));
    const filteredRestaurants = restaurantsWithType.filter((restaurant) =>
      validRestaurantIds.has(restaurant.res_id)
    );

    if (filteredRestaurants.length === 0) {
      return {
        statusCode: 404,
        body: JSON.stringify({
          message: "No restaurants found in the specified city",
        }),
      };
    }

    // Step 5: Fetch featured products
    const productQuery = `
            SELECT m.mid, m.rid, m.dname, m.ddesc, m.dprice, CONCAT('https://static.tossdown.com/images/', i.image) AS image 
            FROM menu m 
            LEFT JOIN images i ON m.mid = i.menuid 
            WHERE m.featured = 1 AND m.rid IN (?)
        `;
    const productQueryParams = [Array.from(validRestaurantIds)];
    const featuredProducts = await queryPromise(
      pool,
      productQuery,
      productQueryParams
    );

    // Step 6: Map branches and products
    const branchMap = branches.reduce((acc, branch) => {
      if (!acc[branch.r_id]) acc[branch.r_id] = [];
      acc[branch.r_id].push(branch);
      return acc;
    }, {});

    const productMap = featuredProducts.reduce((acc, product) => {
      if (!acc[product.rid]) acc[product.rid] = [];
      acc[product.rid].push(product);
      return acc;
    }, {});

    // Step 7: Format and filter final response
    let restaurantsWithDetails = filteredRestaurants.map((restaurant) => ({
      res_id: restaurant.res_id,
      name: restaurant.name,
      category: restaurant.businessCategory, // Use the category from the restaurant object
      domain: restaurant.domain,
      type: restaurant.type,
      logo: restaurant.logo_image,
      branches: branchMap[restaurant.res_id] || [],
      featured_products: productMap[restaurant.res_id] || [],
    }));

    if (featured_products === '1') {
      restaurantsWithDetails = restaurantsWithDetails.filter(restaurant => 
        restaurant.featured_products && restaurant.featured_products.length > 0
      );
    }

    // Apply pagination after all filtering is complete
    const totalCount = restaurantsWithDetails.length;
    const limitNum = limit ? parseInt(limit, 10) : null;
    const offsetNum = offset ? parseInt(offset, 10) : 0;

    if (limitNum) {
      restaurantsWithDetails = restaurantsWithDetails.slice(offsetNum, offsetNum + limitNum);
    }

    return {
      statusCode: 200,
      body: JSON.stringify(restaurantsWithDetails),
    };
  } catch (error) {
    console.error("Error fetching data: ", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal Server Error",
        error: error.message,
      }),
    };
  }
};
