const productModel = require('../../models/productModel');

/**
 * Lambda handler for updating featured items.
 * @param {object} event - The API Gateway event object.
 * @returns {object} - The response object.
 */

// Set default headers
const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*", // or specify your allowed origin
    "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept",
    "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};

module.exports.featuredProducts = async (event) => {
    try {
        // Ensure the request is a POST
        if (event.httpMethod !== 'POST') {
            return {
                statusCode: 405,
                headers,
                body: JSON.stringify({
                    status: 405,
                    message: 'Invalid request method. Please use POST.'
                })
            };
        }

        // Parse the request body
        const input = JSON.parse(event.body);

        // Validate input
        if (!input.items || !Array.isArray(input.items)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    status: 400,
                    message: "Invalid input. 'items' must be an array."
                })
            };
        }

        const items = input.items;
        const response_data = { items: [] };

        // Loop through the items to update their featured status
        for (const item of items) {
            if (typeof item.id !== 'number' || typeof item.featured !== 'number') {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({
                        status: 400,
                        message: "Each item must have 'id' and 'featured' as numbers."
                    })
                };
            }

            const { id, featured } = item;

            try {

                let featuredProduct = await productModel.featuredItem(featured, id);
                if (featuredProduct > 0) {
                    response_data.items.push({ id, featured });
                } else {
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            status: 500,
                            message: `Error updating item ID ${id}. No rows affected.`
                        })
                    };
                }
            } catch (error) {
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({
                        status: 500,
                        message: `Database error: ${error.message}`
                    })
                };
            }
        }

        // Return success response
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                status: 200,
                data: response_data
            })
        };
    } catch (error) {
        // Handle unexpected errors
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                status: 500,
                message: `Internal server error: ${error.message}`
            })
        };
    }
};
