
const orderValidator = require("../../uitls/orders/orderValidator");
const ordersHelper = require("../../uitls/orders/ordersHelper");
const logger = require('../../uitls/logger');

// Set default headers
const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*", // or specify your allowed origin
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};

module.exports.createNewOrder = async (event) => {

    let message = "Bad Request",
        status = 400,
        result = {},
        response = {};

    try {
        // Extract headers
        const headers = event.headers || {};
        const userAgent = headers["User-Agent"] || headers["user-agent"] || "";
        // const referer = headers["Referer"] || headers["referer"] || "";
        // const apiKey = headers["x-api-key"] || "";

        // Block requests from Postman, cURL, and bots
        if (userAgent.includes("PostmanRuntime") || userAgent.includes("curl") || userAgent.includes("python-requests")) {
            throw new Error("Unauthorized request: API access denied.");
        }
        // Extract relevant data from the parsed request data
        const businessId = event.pathParameters.business_id;
        const requestBody = JSON.parse(event.body);

        const validateJson = orderValidator.validateOrderJson(requestBody);

        const mapOrderToCart = await ordersHelper.placeOrderToCart(validateJson);
        // Set success response
        status = 200;
        message = "Success";
        // Set result as the calculated cart
        result = mapOrderToCart;
    } catch (err) {
        // If an error occurs, set the error message
        message = err.message;
    }

    // Set response properties
    response = { status, message, result };

    // Return the response in the required format
    return {
        statusCode: status,
        headers: headers,
        body: JSON.stringify(response),
    };
}


module.exports.bulkOrderUpdate = async (event) => {

    let message = "Bad Request",
        status = 400,
        result = [],
        response = {};

    try {
        // Extract relevant data from the parsed request data
        const businessId = event.pathParameters.business_id;
        const locationId = event.pathParameters.location_id;

        const requestBody = JSON.parse(event.body);
        logger.info("Request Body:", requestBody); // Log request body

        const validateJson = orderValidator.validateBulkOrderJson(requestBody);
        logger.info("Bulk Validation Log:", validateJson); // Log request body
        
        const mapOrderToCart = await ordersHelper.updateBulkOrders(businessId, locationId, validateJson);
        // Set success response
        status = 200;
        message = "Success";
        // Set result as the calculated cart
        result = mapOrderToCart;
        logger.info("Response Body:", mapOrderToCart); // Log request body
    } catch (err) {
        // If an error occurs, set the error message
        message = err.message;
    }

    // Set response properties
    response = { status, message, result };

    // Return the response in the required format
    return {
        statusCode: status,
        headers: headers,
        body: JSON.stringify(response),
    };
}

module.exports.getOrders = async (event) => {

    let message = "Bad Request",
        status = 400,
        getOrdersList = [],
        response = {};

    try {
        // Extract relevant data from the parsed request data
        const businessId = event.pathParameters.business_id;
        const requestBody = JSON.parse(event.body);

        const validateJson = orderValidator.validateGetOrderJson(requestBody);
        
        const getOrdersList = await ordersHelper.getOrders(businessId, validateJson);
        // Set success response
        status = 200;
        message = "Success";
        // Set result as the calculated cart
        result = getOrdersList;
    } catch (err) {
        // If an error occurs, set the error message
        message = err.message;
    }

    // Set response properties
    response = { status, message, result };

    // Return the response in the required format
    return {
        statusCode: status,
        headers: headers,
        body: JSON.stringify(response),
    };
}