# Welcome to Serverless!
#
# This file is the main config file for your service.
# It's very minimal at this point and uses default values.
# You can always add more config options for more control.
# We've included some commented out config examples here.
# Just uncomment any of them to get that config option.
#
# For full config options, check the docs:
#    docs.serverless.com
#
# Happy Coding!

service: serverless-apis
# app and org for use with dashboard.serverless.com
#app: your-app-name
#org: your-org-name

# You can pin your service to only deploy with a specific Serverless version
# Check out our docs for more details
frameworkVersion: "4.14.4"

provider:
  name: aws
  runtime: nodejs16.x
  region: us-east-1
  stage: ${opt:stage, 'dev'}  # Default to 'development' if not specified
  vpc:
    securityGroupIds:
      - sg-0b05a3e2a248205d7
    subnetIds:
      - subnet-040c3e9e469de95ac
      - subnet-0719db5b4304d9248
  iamRoleStatements:
    - Effect: Allow
      Action:
        - sqs:ReceiveMessage
        - sqs:DeleteMessage
        - sqs:GetQueueAttributes
      Resource: arn:aws:sqs:us-east-1:299072694728:OrderEventsQueue

    - Effect: Allow
      Action:
        - secretsmanager:GetSecretValue
      Resource: arn:aws:secretsmanager:us-east-1:299072694728:secret:SENDGRID_API_KEY_PROD-D9VhGr

    - Effect: Allow
      Action:
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: arn:aws:logs:us-east-1:*:*
plugins:
  - serverless-offline # This plugin simulates AWS Lambda and API Gateway on your local machine for testing


custom:
  # serverless-offline:
  #   httpPort: 3000
  #   host: '***********'
  dotenv:
    path: .env.${opt:stage, self:provider.stage}

# you can define service wide environment variables here
#  environment:
#    variable1: value1


functions:
  updateInventory:
    handler: src/inventory/index.handler
    events:
      - http:
          path: update-inventory
          method: post
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  getBusinessSetting:
    handler: src/business/index.getBusinessSetting
    events:
      - http:
          path: /v1/business/{business_id}
          method: get
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  getAllBranches:
    handler: src/business/index.getBranchSetting
    events:
      - http:
          path: /v1/business/{business_id}/locations
          method: get
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  getOneBranch:
    handler: src/business/index.getBranchSetting
    events:
      - http:
          path: /v1/business/{business_id}/locations/{location_id}
          method: get
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  getBranchPickupHours:
    handler: src/business/index.getBranchPickupHours
    events:
      - http:
          path: /v1/business/{business_id}/locations/{location_id}/pickup
          method: get
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  getOrders:
    handler: src/orders/index.getOrders
    events:
      - http:
          path: /v1/business/{business_id}/orders
          method: post
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  createOrder:
    handler: src/orders/index.createNewOrder
    events:
      - http:
          path: /v1/business/{business_id}/order
          method: post
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  bulkOrder:
    handler: src/orders/index.bulkOrderUpdate
    events:
      - http:
          path: /v1/business/{business_id}/location/{location_id}/bulk_order
          method: post
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  chargePayment:
      handler: src/payments/index.chargeOnlinePayment
      events:
        - http:
            path: /v1/business/{business_id}/payment/{pay_type}/charge
            method: post
            cors: true # Enable CORS if your Lambda function is invoked from a web page
  registerStripeCustomer:
      handler: src/payments/index.registerStripeCustomer
      events:
        - http:
            path: /v1/business/{business_id}/payment/{pay_type}/register_stripe_customer
            method: post
            cors: true # Enable CORS if your Lambda function is invoked from a web page
  fetchCustomerPaymentDetails:
      handler: src/payments/index.fetchCustomerDetails
      events:
        - http:
            path: /v1/business/{business_id}/payment/{pay_type}/customer/{customer_id}/cards
            method: get
            cors: true # Enable CORS if your Lambda function is invoked from a web page
  addNewCardToCustomerPaymentDetails:
      handler: src/payments/index.addCustomerDetails
      events:
        - http:
            path: /v1/business/{business_id}/payment/{pay_type}/customer/{customer_id}/cards
            method: put
            cors: true # Enable CORS if your Lambda function is invoked from a web page
  paymentStatusUpdate:
      handler: src/payments/index.webhookOnlinePayment
      events:
        - http:
            path: /v1/payment/{pay_type}
            method: post
            cors: true # Enable CORS if your Lambda function is invoked from a web page
  EcomMetrixCalculation:
    handler: src/stats/index.getMetrics
    timeout: 60 # 60 seconds timeout
    events:
      - http:
          path: /v1/business/{business_id}/stats
          method: get
          request:
            parameters:
              paths:
                business_id: true
              querystrings:
                startDate: true
                endDate: true
          cors: true # Enable CORS if your Lambda function is invoked from a web page
  featuredProducts:
    handler: src/products/index.featuredProducts
    events:
      - http:
          path: /v1/business/{business_id}/products/featured
          method: POST
          cors: true
  getRestaurantsAndBranches:
    handler: src/business/index.getRestaurantsAndBranches
    events:
      - http:
          path: /v1/finder
          method: get
          cors: true 