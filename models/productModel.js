const db = require('../config/database');

/**
 * Get Business Details
 * @param {number} businessId - Business ID
 * @return {Promise<object>} - Business details
 */

module.exports.featuredItem = async (featured, mid) => {
    try {
        const sql = "UPDATE menu SET featured = ? WHERE mid = ?";
        let result = await executeQuery(sql, [featured, mid]);
        if (result.affectedRows > 0) {
            return 1;
        } else {
            return 0;
        }
    } catch (error) {
        throw error;
    }
};

// Function to execute a database query
function executeQuery(sqlQuery, mappedFields) {
    return db.queryPromise(db.pool, sqlQuery, mappedFields);
}
