const db = require('../config/database');

/**
 * Get Business Details
 * @param {number} businessId - Business ID
 * @return {Promise<object>} - Business details
 */
module.exports.getBusinessDetails = async (businessId) => {
    try {
        const sql = "SELECT b.name, b.logo, b.currencycode, b.aws_flag, b.time_zone, b.username, m.discount, m.tax, m.minimum_spend, m.inventory, m.delivery_charges_tax, m.tax_type, m.settings, m.contact_phone, m.email, m.contact_name, m.tax_before_discount, m.preparation_time, m.decimal_places FROM restaurants b INNER JOIN menu_option m ON b.r_id = m.r_id WHERE b.r_id = ?";
        let result = await executeQuery(sql, [businessId]);

        const count = result.length || 0;
        if (count > 0) {
            let firstRow = result[count - 1];
            firstRow.settings = JSON.parse(firstRow.settings);
            return firstRow;
        }
        let firstRow = result[0];
        firstRow.settings = JSON.parse(firstRow.settings);
        return firstRow;
    } catch (error) {
        throw error;
    }
};

/**
 * Get Branch Details
 * @param {number} businessId - Business ID
 * @param {number} branchId - Branch ID
 * @return {Promise<Array>} - Array of branch details
 */
module.exports.getBranchDetails = async (businessId, branchId) => {
    try {
        const branchQuery = (branchId) ? " AND a.id = ?" : "";
        const sql = "SELECT a.id, a.r_id, a.address, a.location, a.country, a.city, a.location, a.phoneno, a.lat, a.lng, a.status, a.email, a.delivery, a.pickup, a.reservation, a.delivery_service, a.time_zone, a.settings, a.delivery_settings, a.email_settings, a.sms_settings, a.timing FROM restaurants b INNER JOIN addresses a ON b.r_id = a.r_id WHERE b.r_id = ? AND a.status = ''" + branchQuery;
        let results = await executeQuery(sql, [businessId, branchId]);

        return results;
    } catch (error) {
        throw error;
    }
};

// Function to execute a database query
function executeQuery(sqlQuery, mappedFields) {
    return db.queryPromise(db.pool, sqlQuery, mappedFields);
}
