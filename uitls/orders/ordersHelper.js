const ordersModel = require('../../models/ordersModel');
const moment = require('moment');
const notificationHelper = require('../sendNotification');
const posHelper = require('../posHelper');
const emailHelper = require('../comunication/emailHelper');
const loyaltyHelper = require('../loyalty/loyaltyHelper');
const businessModal = require("../../models/businessModel");
const logger = require('../../uitls/logger');


module.exports.placeOrderToCart = async (orderDetails) => {
    try {

        const businessId = orderDetails.business_id
        const businessSettings = await businessModal.getBusinessDetails(businessId);

        // Add receipt numbering
        orderDetails.receipt_no = generateReceiptNumber();

        // Map order details
        let orderData = mapOrderDetails(orderDetails);

        orderData.date = new Date()
        orderData.sales_date = new Date()

        // Add order to the cart
        const cartResponse = await ordersModel.addOrder(orderData);

        // Assign the order ID to orderDetails
        orderDetails.order_id = cartResponse.insertId;

        const orderDetailsGet = await ordersModel.getOrder(businessId, orderDetails.order_id);
        // Map order item details
        const mapItemDetails = await mapOrderItemDetails(orderDetails);

        // Add order details to the cart
        const cartDetailResponse = await ordersModel.addOrderDetails(mapItemDetails);

        // Create a final cart object combining order data and item details
        const finalCart = {
            ...orderDetailsGet,
            order_id: orderDetails.order_id,
            time_zone: businessSettings.time_zone,
            order_details: mapItemDetails
        };

        const transctionResponse = await ordersModel.addTransactionTimeline(orderDetails);


        const notificationResponse = await notificationHelper.sendPusherNotification(finalCart);
        const partenerAppNotificationResponse = await notificationHelper.sendFcmNotification(finalCart, businessSettings);
        // const updatePosData = await posHelper.posOrderDetails(businessId, finalCart);
        if (finalCart.loyalty_points > 0) {
            const loyaltyTransaction = await loyaltyHelper.createLoyaltyRedemptionRequest(businessId, finalCart);
            console.log(loyaltyTransaction)
        }
        const emailResponse = await emailHelper.sendEmailNotification(businessId, businessSettings, finalCart, 'Pending');

        return finalCart;
    } catch (error) {
        // Handle errors
        console.error("Error placing order to cart:", error);
        throw error; // Rethrow the error to be handled by the caller
    }
};

module.exports.updateBulkOrders = async (businessId, locationId, orderDetails) => {
    try {

        const businessSettings = await businessModal.getBusinessDetails(businessId);
        const transactionStatuses = await ordersModel.getTransactionStatus();
        const ordersList = orderDetails.orders;
        const finalCart = [];

        for (let i = 0; i < ordersList.length; i++) {

            let orderRequesOrderDetail = ordersList[i];

            const recieptNo = orderRequesOrderDetail.receipt_no;
            const status = orderRequesOrderDetail.status;

            const orderWithReciept = await ordersModel.getOrderByRecieptNo(businessId, recieptNo);
            let orderData = mapOrderDetails(orderRequesOrderDetail);
            const currentDate = new Date();

            logger.info("Order Detail:", orderRequesOrderDetail); // Log request body

            if (orderWithReciept.count !== 0) {

                const orderDetail = orderWithReciept.result[0];
                const orderCurrentTransaction = await ordersModel.getOrderTransactionStatus(orderDetail.orderid);

                orderData.date = orderDetail.date
                orderData.sales_date = orderDetail.sales_date

                const cartResponse = await ordersModel.updateOrder(businessId, recieptNo, orderData);
                orderRequesOrderDetail.order_id = orderDetail.orderid;
                if (cartResponse.changedRows == 1) {

                    const mapItemDetails = await mapOrderItemDetails(orderRequesOrderDetail);
                    const removeCartDetailResponse = await ordersModel.removeOldOrderDetails(orderDetail.orderid);

                    const cartDetailResponse = await ordersModel.addOrderDetails(mapItemDetails);

                    const reponseCart = {
                        order_id: orderDetail.orderid,
                        receipt_no: recieptNo,
                        action: "update",
                        datetime: moment(currentDate).format('YYYY-MM-DD HH:mm:ss')
                    };

                    const posOrder = {
                        ...orderRequesOrderDetail,
                        orderid: orderDetail.orderid,
                        paid: orderData.paid,
                        order_details: mapItemDetails,
                        time_zone: businessSettings.time_zone
                    };
                    finalCart.push(reponseCart);
                    const updatePosData = await posHelper.posOrderDetails(businessId, posOrder);

                    if (orderCurrentTransaction.status != orderData.status) {

                        const getTransactionStatus = await getTransactionStatusId(transactionStatuses, orderData.status);
                        const transctionResponse = await ordersModel.addTransactionTimeline(orderRequesOrderDetail, orderData.status, getTransactionStatus);
                    }

                    if (orderRequesOrderDetail.name != "" && orderRequesOrderDetail.email != "") {
                        const emailResponse = await emailHelper.sendEmailNotification(businessId, businessSettings, posOrder, status);
                    }

                }
            } else {

                orderData.date = new Date()
                orderData.sales_date = new Date()
                const cartResponse = await ordersModel.addOrder(orderData);
                orderRequesOrderDetail.order_id = cartResponse.insertId;
                if (cartResponse.insertId != 0) {
                    const mapItemDetails = await mapOrderItemDetails(orderRequesOrderDetail);
                    cartDetailResponse = await ordersModel.addOrderDetails(mapItemDetails);

                    const reponseCart = {
                        order_id: orderRequesOrderDetail.order_id,
                        receipt_no: recieptNo,
                        action: "new",
                        datetime: moment(currentDate).format('YYYY-MM-DD HH:mm:ss')
                    };

                    const posOrder = {
                        ...orderRequesOrderDetail,
                        orderid: orderRequesOrderDetail.order_id,
                        order_details: mapItemDetails,
                        time_zone: businessSettings.time_zone
                    };
                    finalCart.push(reponseCart);
                    const updatePosData = await posHelper.posOrderDetails(businessId, posOrder);
                    const getTransactionStatus = await getTransactionStatusId(transactionStatuses, orderData.status);
                    const transctionResponse = await ordersModel.addTransactionTimeline(orderRequesOrderDetail, orderData.status, getTransactionStatus);

                    if (orderRequesOrderDetail.name != "" && orderRequesOrderDetail.email != "") {
                        const emailResponse = await emailHelper.sendEmailNotification(businessId, businessSettings, posOrder, status);
                    }
                }
            }

        }

        const pusherHelper = {
            r_id: businessId,
            bid: locationId,
        }
        const notificationResponse = await notificationHelper.sendPusherNotification(pusherHelper, "POS", "bulk-orders");
        // const emailResponse = await emailHelper.sendEmailNotification(businessId, businessSettings, finalCart, 'Pending');

        return finalCart;
    } catch (error) {
        // Handle errors
        console.error("Error placing order to cart:", error);
        throw error; // Rethrow the error to be handled by the caller
    }
};

module.exports.getOrderDetails = async (businessId, orderId) => {
    try {

        const orderGet = await ordersModel.getOrder(businessId, orderId);
        const orderDetailsGet = await ordersModel.getOrderDetails(businessId, orderId);
        // Create a final cart object combining order data and item details
        let finalCart = {
            ...orderGet,
            order_details: orderDetailsGet
        };

        return finalCart;
    } catch (error) {
        // Handle errors
        console.error("Error fetching order to cart:", error);
        throw error; // Rethrow the error to be handled by the caller
    }
};

module.exports.getOrders = async (businessId, requestBody) => {
    try {

        const finalOrderList = [];
        const businessSettings = await businessModal.getBusinessDetails(businessId);
        const orderRequest = {
            from_date: moment(requestBody.from_date).format('YYYY-MM-DD'),
            to_date: moment(requestBody.to_date).format('YYYY-MM-DD'),
            order_id: requestBody.order_id,
            offset: requestBody.offset,
            limit: requestBody.limit,
            location_id: requestBody.location_id,
            is_paid: requestBody.is_paid,
            time_zone: businessSettings.time_zone,
            payment_type: requestBody.payment_type,
        }

        const orderGet = await ordersModel.getBulkOrder(businessId, orderRequest);

        const ordersCount = orderGet.count;
        const ordersList = orderGet.result;

        if (ordersCount > 0) {
            for (let i = 0; i < ordersCount; i++) {

                const locationId = ordersList[i].bid;
                const branchSettings = await businessModal.getBranchDetails(
                    businessId,
                    locationId
                );
                const mappedOrderData = mapOrderGet(ordersList[i], businessSettings, branchSettings)
                const orderId = mappedOrderData.order_id;
                const orderDetailsGet = await ordersModel.getOrderDetails(businessId, orderId);

                const orderData = {
                    ...mappedOrderData,
                    order_details: orderDetailsGet
                }
                finalOrderList.push(orderData);
            }
        }
        return finalOrderList;
    } catch (error) {
        // Handle errors
        console.error("Error fetching order to cart:", error);
        throw error; // Rethrow the error to be handled by the caller
    }
};

// Function to generate receipt number
const generateReceiptNumber = () => {
    return moment.utc().valueOf() * Math.floor(Math.random() * (1000 - 100) + 100);
};

// Function to map order details
function mapOrderDetails(orderDetails) {

    const orderTypeFlag = (orderDetails.order_type == "delivery") ? "0" : "1";

    const binaryStatus = setBinaryStatus(orderDetails.status)
    let orderData = {
        r_id: orderDetails.business_id,
        uid: orderDetails.uid,
        invoice_id: orderDetails.invoice_id || 0,
        name: orderDetails.name,
        date: orderDetails.current_date,
        delivery_date: orderDetails.delivery_date,
        total: orderDetails.total,
        tax: orderDetails.tax,
        tax_value: orderDetails.tax_value,
        discount: orderDetails.discount,
        discount_value: orderDetails.discount_value,
        gtotal: orderDetails.gtotal,
        new_gtotal: orderDetails.gtotal,
        // date: new Date(),
        // sales_date: new Date(),
        status: orderDetails.status || "Pending",
        status_id: 1,
        address: orderDetails.address,
        ordertype: orderDetails.order_type,
        order_type: orderDetails.order_type,
        mobile_phone: orderDetails.mobile_phone,
        landline_phone: orderDetails.landline_phone || "",
        note: orderDetails.status_comment || orderDetails.note,
        email: orderDetails.email,
        delivery_charges: orderDetails.delivery_charges || 0,
        delivery_tax: orderDetails.delivery_tax || 0,
        delivery_tax_value: orderDetails.delivery_tax_value || 0,
        payment_type: orderDetails.payment_type || 0,
        country: orderDetails.country || "",
        city: orderDetails.city,
        area: orderDetails.area,
        source: orderDetails.source,
        reference: '',
        bid: orderDetails.bid || 0,
        postal_code: orderDetails.postal_code || '',
        flag: orderDetails.flag || 0,
        loyalty_points: orderDetails.loyalty_points || 0,
        custom_code: 0, // Replace with actual value
        custom_code_id: 0, // Replace with actual value
        custom_code_type: 2, // Replace with actual value
        custom_code_discount: orderDetails.custom_code_discount, // Replace with actual value
        custom_code_discount_value: orderDetails.custom_code_discount_value, // Replace with actual value
        user_latitude: orderDetails.user_latitude || '', // Replace with actual value
        user_longitude: orderDetails.user_longitude || '', // Replace with actual value
        order_type_flag: orderTypeFlag,
        table_no: orderDetails.table_no || "", // Replace with actual value
        eatout_manager_id: orderDetails.eatout_manager_id || 0, // Replace with actual value
        pos_id: orderDetails.pos_id || "", // Replace with actual value
        pay_type: orderDetails.pay_type || "", // Replace with actual value
        receipt_no: orderDetails.receipt_no,
        paid: orderDetails.is_paid || 0, // Replace with actual value
        tip: orderDetails.tip,
        subscription: JSON.stringify(orderDetails.subscription),
        cash_received: orderDetails.cash_received || 0, // Replace with actual value
        cash_change: orderDetails.cash_change || 0, // Replace with actual value
        penny_rounding: orderDetails.penny_rounding || 0, // Replace with actual value
        pay_owner: orderDetails.pay_owner || "", // Replace with actual value
        guests: orderDetails.guests || "", // Replace with actual value
        binary_status: binaryStatus || "Pending", // Replace with actual value
        is_partial_refund: orderDetails.is_partial_refund || 0, // Replace with actual value
        service_charges: orderDetails.service_charges, // Replace with actual value
        cash_change: orderDetails.cash_change || 0, // Replace with actual value
        cash_received: orderDetails.cash_received || 0,
        // tax_type: orderDetails.tax_type || "", // Replace with actual value
        is_splitted: orderDetails.is_splitted || 0, // Replace with actual value
        splitted_payments: JSON.stringify(orderDetails.splitted_payments) || '[]', // Replace with actual value
        canada_post: 0,// Replace with actual value
        updated_at: new Date(),// Replace with actual value
        last_sync: new Date(),// Replace with actual value
    }

    if (orderDetails.order_type != "delivery") {
        orderData.delivery = orderDetails.delivery;
    }

    return orderData;
}

// Function to execute a database query
const mapOrderItemDetails = async (orderDetails) => {

    const orderItems = orderDetails.items;
    const mappedItems = orderItems.map(item => ({
        orderid: orderDetails.order_id,
        dname: item.name,
        dqty: item.qty,
        dprice: +parseFloat(item.price),
        dtotal: +parseFloat(item.total),
        item_level_grand_total: item.item_level_grand_total, // Assuming this should be the total for the specific item
        comment: item.comment,
        option_set: JSON.stringify(item.option_set),
        menu_item_id: item.id,
        discount: item.discount,
        item_level_discount_value: item.item_level_discount_value,
        coupon_discount: item.coupon_discount,
        coupon_discount_value: item.coupon_discount_value,
        tax: +parseFloat(item.tax),
        item_level_tax_value: item.item_level_tax_value,
        weight_value: +parseFloat(item.weight_value) || 0,
        weight_unit: item.weight_unit || "",
        category_id: item.category_id,
        brand_id: item.brand_id || ""
    }));

    return mappedItems;
}

// Function to get transaction_status_id based on status
const getTransactionStatusId = async (transactionStatuses, status) => {
    // Find the object with the matching status
    const statusObject = transactionStatuses.find(obj => obj.status === status);
    // If object found, return its transaction_status_id, otherwise return null
    return statusObject ? statusObject.transaction_status_id : null;
}

function mapOrderGet(orderDetails, businessSettings, branchSettings) {
    let orderData = {
        address: orderDetails.address,
        canada_post: orderDetails.canada_post,
        cash_received: orderDetails.cash_received,
        change: orderDetails.cash_change,
        cnic: orderDetails.cnic,
        color: "",
        currency: businessSettings.currencycode,
        custom_code_discount: orderDetails.custom_code_discount,
        custom_code_discount_value: orderDetails.custom_code_discount_value,
        custom_code_id: orderDetails.custom_code_id,
        custom_code_type: orderDetails.custom_code_type,
        date: orderDetails.date, //to be update,
        deciaml_places: businessSettings.decimal_places,
        delivery_charges: orderDetails.delivery_charges,
        delivery_date: orderDetails.delivery_date,
        delivery_tax: orderDetails.delivery_tax,
        delivery_tax_value: orderDetails.delivery_tax_value,
        discount: orderDetails.discount,
        discount_value: orderDetails.discount_value,
        location_id: branchSettings.id,
        location_address: branchSettings.address,
        eatout_manager: orderDetails.eatout_manager_id,
        grand_total: orderDetails.gtotal,
        guests: orderDetails.guests,
        is_partial_refund: orderDetails.is_partial_refund,
        landline_number: orderDetails.landline_number,
        is_splitted: orderDetails.is_splitted,
        loyalty_points: orderDetails.loyalty_points,
        mobile_phone: orderDetails.mobile_phone,
        name: orderDetails.name,
        note: orderDetails.note,
        order_id: orderDetails.orderid,
        order_type: orderDetails.ordertype,
        order_type_flag: orderDetails.order_type_flag,
        pay_owner: orderDetails.pay_owner,
        pay_type: orderDetails.pay_type,
        payment_type: orderDetails.payment_type, //should be string,
        penny_rounding: orderDetails.penny_rounding,
        pos_id: orderDetails.pos_id,
        postal_code: orderDetails.postal_code,
        pre_auth: orderDetails.pre_auth,
        receipt_no: orderDetails.receipt_no,
        sales_date: orderDetails.sales_date,
        service_charges: orderDetails.service_charges,
        source: orderDetails.source,
        status: orderDetails.status,
        splitted_payments: orderDetails.splitted_payments,
        table_no: orderDetails.table_no,
        tax: orderDetails.tax,
        tax_value: orderDetails.tax_value,
        tip: orderDetails.tip,
        total: orderDetails.total,
        table_no: orderDetails.table_no,
        user_area: orderDetails.area,
        user_city: orderDetails.city,
        user_country: orderDetails.country,
        user_email: orderDetails.email,
        user_latitude: orderDetails.user_latitude,
        user_longitude: orderDetails.user_longitude,
    }

    return orderData;
}

function setBinaryStatus(orderStatus) {

    let binaryStatus = "Pending"
    if (orderStatus == "Pending") {
        binaryStatus = "Pending"
    } else if (orderStatus == "Refund" || orderStatus == "Cancel") {
        binaryStatus = "Cancel"
    } else {
        binaryStatus = "Confirmed"
    }

    return binaryStatus;
}