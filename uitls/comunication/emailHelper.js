const businessModal = require("../../models/businessModel");
const moment = require('moment');
const AWS = require("aws-sdk");
const secretsManager = new AWS.SecretsManager();
const client = require("../client")
const dotenv = require('dotenv'); // Importing dotenv for environment variables
dotenv.config(); // Loading environment variables from .env file

// Function to send Pusher notification for a new order
module.exports.sendEmailNotification = async (businessId, businessSettings, orderData, status) => {
    try {

        const orderId = orderData.orderid;

        const { send_email: emailEnabled, logo: businessLogo, currencycode: businessCurrency, decimal_places: decimalPlaces, contact_name: businessName, time_zone: businessTimeZone } = businessSettings;
        let { contact_phone: businessPhone, email: businessEmail } = businessSettings;

        // Fetch branch details using business and location IDs
        const branchSettings = await businessModal.getBranchDetails(
            businessId,
            orderData.bid
        );


        businessPhone = (branchSettings[0].phoneno) ? branchSettings[0].phoneno : businessPhone;
        businessEmail = (branchSettings[0].email) ? branchSettings[0].email : businessEmail;

        let emailBusinessSettings = {
            name: businessName,
            email: businessEmail,
            phone: businessPhone,
            timezone: businessTimeZone,
            logo: businessLogo,
            currency: businessCurrency,
            decimal_places: decimalPlaces,
        }
        const statusDetails = await getStatusDetails(orderId, businessName, status);

        const subject = statusDetails.subject;

        const emailSettings = {
            button_bg: statusDetails.button_bg_color,
            title: statusDetails.title,
            button_text: statusDetails.button_text_color,
            order_status: status,
            subject: subject,
            return_policy: statusDetails.return_policy,
            paid: orderData.paid,
            status_icon: process.env.EMAIL_RECEIVED_ICON,
            details_check: statusDetails.details_check,
            pickup_message: ""
        }
        if (statusDetails.status == true) {
            const response = await createSendgridPayload(orderData, emailBusinessSettings, emailSettings)
            const apiSendGrid = await sendPendingEmail(response);

            const responseManager = await createSendgridPayload(orderData, emailBusinessSettings, emailSettings, "manager")
            console.log(responseManager)
            const apiSendGridManager = await sendPendingEmail(responseManager);

            console.log(apiSendGridManager)
        }

    } catch (error) {
        // Logging any errors that occur during the try block
        console.error('Error in Email Settings:', error.message);
        // Returning the error
        return error;
    }
}

const createSendgridPayload = async (orderDetails, businessDetails, emailSettings, emailType = "") => {

    const itemsCount = orderDetails.order_details.length
    const customerNote = orderDetails.note;
    const customMessage = "";
    let subject = emailSettings.subject;
    let title = emailSettings.title;

    if (emailType == "manager" && emailSettings.order_status == "Pending") {
        subject = "New Order #" + orderDetails.orderid + " Request Received - Pending";
        title = "New Order Received";
    }

    //Define Send To Settings
    const sendToEmail = (emailType != "manager") ? orderDetails.email : businessDetails.email;
    const sendToName = (emailType != "manager") ? orderDetails.name : businessDetails.name;

    //Define Send From Settings
    const sendFromEmail = businessDetails.email;
    const sendFromName = businessDetails.name;

    const visibility = {
        discount: (orderDetails.discount_value) ? true : false,
        tax: (orderDetails.tax_value) ? true : false,
        tip: (orderDetails.tip) ? true : false,
        serviceCharges: (orderDetails.service_charges) ? true : false,
    }

    let emailPickupTime = "";
    if (orderDetails.order_type_flag == "1") {
        if (orderDetails.delivery != "0000-00-00 00:00:00") {
            emailPickupTime = "Date: " + moment(orderDetails.delivery).format('dddd, MMMM DD, YYYY HH:mm:ss');
        }
    } else if(orderDetails.delivery_date != "0000-00-00 00:00:00"){
            emailPickupTime = "Date: " + moment(orderDetails.delivery_date).format('dddd, MMMM DD, YYYY HH:mm:ss');
    }

    let custom_code = null, custom_code_discount = 0;
    if (orderDetails?.custom_code_discount_value > 0) {
        custom_code = orderDetails.custom_code;
        custom_code_discount = orderDetails.custom_code_discount_value;
    }
    const orderItems = await mapOrderItems(orderDetails.order_details, businessDetails.currency)

    const requestBody = {
        from: {
            email: sendFromEmail,
            name: sendFromName
        },
        personalizations: [
            {
                to: [
                    {
                        email: sendToEmail,
                        name: sendToName
                    }
                ],
                dynamic_template_data: {
                    subject: subject,
                    items: orderItems,
                    restaurant_logo: "https://static.tossdown.com/logos/" + businessDetails.logo,
                    email_icon: process.env.EMAIL_RECEIVED_ICON,
                    restaurant_contact: businessDetails.phone,
                    restaurant_email: businessDetails.email,
                    button_bg: emailSettings.button_bg,
                    button_text: emailSettings.button_text,
                    user: orderDetails.name,
                    title: title,
                    name: orderDetails.name,
                    phone: orderDetails.mobile_phone,
                    address: orderDetails.address,
                    email: orderDetails.email,
                    details_check: emailSettings.details_check,
                    show_banner: false,
                    email_banner: false,
                    paid: emailSettings.paid,
                    return_policy: emailSettings.return_policy,
                    customer_note: customerNote,
                    custom_message: customMessage,
                    order_total: orderDetails.gtotal,
                    sub_total: orderDetails.total,
                    delivery_charges: orderDetails.delivery_charges,
                    order_type: orderDetails.order_type,
                    tip: orderDetails.tip,
                    service_charges: orderDetails.service_charges,
                    discount: orderDetails.discount_value,
                    discount_percent: orderDetails.$discount_percent,
                    order_tax_check: visibility.tax,
                    order_discount_check: visibility.discount,
                    order_service_charges_check: visibility.serviceCharges,
                    order_tip_check: visibility.tip,
                    tax_percent: orderDetails.tax,
                    tax_amount: orderDetails.tax_value,
                    item_count: itemsCount,
                    currency: businessDetails.currency,
                    order_no: orderDetails.orderid,
                    custom_code: custom_code,
                    custom_code_discount: custom_code_discount,
                    order_status: emailSettings.order_status,
                    order_time: emailPickupTime
                }
            }
        ],
        template_id: process.env.TEMPLATE_ID
    }
    return JSON.stringify(requestBody);
}

const mapOrderItems = async (orderItems, businessCurrency) => {
    const mappedData = orderItems.map(row => {
        const price = row.item_level_grand_total;
        const order_comment = row.comment;
        const comment_check = order_comment ? true : false;
        const dprice = row.dtotal;
        const discount_check = (row.discount != 0) ? true : false;
        const product_code = "";
        const model_no_check = product_code ? true : false;
        const tax_check = row.tax ? true : false;
        const item_tax = row.item_level_tax_value;
        const item_discount = row.item_level_discount_value;
        const currency = businessCurrency;
        const weight = (row.weight_value) ? row.weight_value + row.weight_unit : ""
        return {
            "text": row.dname,
            "price": price,
            "count": row.dqty,
            "comment": order_comment,
            "comment_check": comment_check,
            "orignal_price": dprice,
            "discount_check": discount_check,
            "model_no": product_code,
            "model_no_check": model_no_check,
            "tax_check": tax_check,
            "tax": item_tax,
            "discount_value": item_discount,
            "currency": currency,
            "removed_check": "false",
            "weight": weight
        };
    });

    return mappedData;
}

const getStatusDetails = async (orderId, businessName, status) => {

    let statusSettings = {};

    switch (status) {
        case "Pending":
            statusSettings = {
                status: true,
                subject: "Order #" + orderId + " Request Received at " + businessName + "" + " - Pending",
                title: "Your Order is Received!",
                button_text_color: "#FDC103",
                button_bg_color: "#FEF9E5",
                return_policy: true,
                details_check: true,
            }
            break;
        case "Confirmed":
            statusSettings = {
                status: true,
                subject: "Order #" + orderId + " Confirmation",
                title: "Your Order is Confirmed!",
                button_text_color: "#0057D9",
                button_bg_color: "#E5EEFB",
                return_policy: true,
                details_check: true,
            }
            break;
        case "Inprocess":
            statusSettings = {
                status: true,
                subject: "Order #" + orderId + " is now In-process",
                title: "Your Order is In-process!",
                button_text_color: "#FF7901",
                button_bg_color: "#FFF1E5",
                return_policy: false,
                details_check: false,
            }
            break;
        default:
            statusSettings = {
                status: false,
            }
    }
    return statusSettings;
}

const getSendGridAPIKey = async () => {
    try {
      const secretData = await secretsManager.getSecretValue({ SecretId: "SENDGRID_API_KEY_PROD" }).promise();
      return JSON.parse(secretData.SecretString).SENDGRID_API_KEY;
    } catch (error) {
      console.error("Error fetching SendGrid API key:", error);
      throw new Error("Failed to retrieve API key");
    }
}
const sendPendingEmail = async (emailDetails) => {

    const apiURL = process.env.SENDGRID_API_URL
    const apiKey = await getSendGridAPIKey();

    const headers = {
        'Authorization': 'Bearer ' + apiKey,
        'content-type': "application/json"
    }
    // Sending the request to Pusher
    const response = await client.post(apiURL, emailDetails, { headers: headers });
    return response;
}