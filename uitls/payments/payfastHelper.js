const ordersHelper = require('../orders/ordersHelper');
const crypto = require('crypto');
const integrationModel = require('../../models/integrationModel');
const orderModel = require('../../models/ordersModel');
const client = require("../client")


module.exports.chargePayFastPayment = async (businessId, paymentDetails) => {
    console.log(paymentDetails);
    let status = 400;
    let orderStatus = processPayment = response = {};
    try {
        let orderId = paymentDetails.order_id;
        console.log(businessId);
        // Fetch integration details
        const fetchIntegrationData = await integrationModel.getIntegrationDetails(businessId, 'payment', 44, true);
        const fetchOrder = await orderModel.getOrder(businessId, orderId); // Fetch order details
        console.log("fetchOrder");
        console.log(fetchOrder.gtotal);
        const fetchBranchWiseIntegrationData = fetchIntegrationData.settings ? JSON.parse(fetchIntegrationData.settings) : {};
        if (fetchIntegrationData && fetchIntegrationData !== "undefined") {
            const username = fetchIntegrationData.username;
            const merchantId = fetchIntegrationData.sender;
            const securedKey = fetchIntegrationData.key;
            if (!securedKey) {
                response = {
                    status: 404,
                    message: "Unauthorized - API key missing",
                    result: {},
                };
                return response;
            }
            // Check if invoice_id already exists
            if (fetchOrder.invoice_id) {
                const verifyUrl = `https://tossdown.site/api/verify_payfast_order_status?basket_id=${fetchOrder.invoice_id}&res_id=${businessId}&bid=0`;
                const verifyResponse = await client.get(verifyUrl);
                if (verifyResponse?.data?.code === 200 && verifyResponse?.data?.data?.status === 'Success') {
                    response = {
                        status: 200,
                        message: `Payment already confirmed for Order ID: ${orderId}`,
                        result: {
                            order_id: orderId,
                            invoice_id: fetchOrder.invoice_id,
                        },
                    };
                    return response;
                }
            }
            // Generate a new invoice ID if it doesn't exist
            const invoiceId = parseInt(`${orderId}${Math.floor(Math.random() * 10000)}`, 10);

            // Update invoice ID against the order
            const updateInvoiceUrl = 'https://tossdown.com/api/update_invoice_id';
            const updateInvoiceBody = new URLSearchParams();
            updateInvoiceBody.append('order_id', orderId);
            updateInvoiceBody.append('invoice_id', invoiceId);

            const updateInvoiceResponse = await client.post(updateInvoiceUrl, updateInvoiceBody.toString(), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });
            if (!updateInvoiceResponse || updateInvoiceResponse.status !== 200) {
                response = {
                    status: 500,
                    message: "Failed to update invoice ID",
                    result: {},
                };
                return response;
            }
            let fetchBranchWiseAccount = await getBranchWiseAccount(
                fetchBranchWiseIntegrationData,
                username,
                merchantId,
                securedKey,
                paymentDetails.branch_id
            );
            const tokenUrl = `${fetchIntegrationData.url}?MERCHANT_ID=${fetchBranchWiseAccount.merchantId}&SECURED_KEY=${fetchBranchWiseAccount.securedKey}&TXNAMT=${fetchOrder.gtotal}&BASKET_ID=${invoiceId}&CURRENCY_CODE=${paymentDetails.currency_code}`;
            console.log(tokenUrl);
            const tokenResponse = await client.get(tokenUrl);
            const token = tokenResponse.data.ACCESS_TOKEN !== undefined ? tokenResponse.data.ACCESS_TOKEN : null;
            if (!token) {
                response = {
                    status: 404,
                    message: "Invalid Merchant ID / Secured Key",
                    result: {},
                };
                return response;
            }

            response = {
                status: 200,
                message: "Token Successful",
                result: {
                    order_id: orderId,
                    merchant_id: fetchBranchWiseAccount.merchantId,
                    merchant_name: fetchBranchWiseAccount.userName,
                    store_id: fetchBranchWiseAccount.storeId,
                    token: token,
                    invoice_id: invoiceId, // Include the invoice ID in the response
                },
            };
            return response;
        }
        response = {
            status: 404,
            message: "No Integrations available",
            result: {},
        };
        return response;

    } catch (error) {
        console.error("Error:", error.response ? error.response.data : error.message);
        response = { status, message: error.response ? error.response.data : error.message };
    }
    return response;
};

function renderPaymentFormHtml(payment_data) {

    return `<!DOCTYPE html>
    <html>
        <head>
            <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity "sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
            <title>PayFast WebCheckout Integration Demo</title>
        </head>
        <body>
            <div class="container" >
                <h2>PayFast WebCheckout Integration Demo</h2>
                
                <div class="card">
    
                    <div class="card-body">
                        <div class="card-header">
                            PayFasy Web Checkout - Example Code
                        </div>
                        <!--
                            Submit form to PayFast WebCheckout
                        --->
                        <form method="post" id="payment_form" action = "https://ipguat.apps.net.pk/Ecommerce/api/Transaction/PostTransaction"> 
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label>Merchant ID</label>
                                        <INPUT class="form-control" type="TEXT" NAME="MERCHANT_ID"  VALUE="${payment_data.merchant_id}"> 
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label>Merchant Name</label>
                                        <INPUT  class="form-control" type="TEXT" NAME="MERCHANT_NAME" value='${payment_data.merchant_name}'> 
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group" >
                                        <label>Token</label>
                                        <!--
                                            Token value which was fetched earilier
                                        -->
                                        <INPUT  class="form-control"  type="TEXT" NAME="TOKEN" VALUE="${payment_data.token}"  data-toggle="tooltip" role="tooltip" title="Temporary Token"> 
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                <div class="form-group">
                                    <label>Customer Email</label>
                                    <INPUT class="form-control"  type="TEXT" NAME="CUSTOMER_EMAIL_ADDRESS" VALUE="${payment_data.email_address}"> 
                                </div>
                            </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
    
                                    <div class="form-group">
                                        <label>Amount</label>
                                        <INPUT  class="form-control" type="TEXT" NAME="TXNAMT" VALUE = "${payment_data.amount}"> 
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label>Customer Mobile Number</label>
                                        <INPUT  class="form-control" type="TEXT" NAME="CUSTOMER_MOBILE_NO" VALUE="${payment_data.mobile_phone}"> 
                                    </div>
                                </div>
                            </div>
    
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label>Success CallBack URL</label>
                                        <INPUT  class="form-control" type="TEXT" NAME="SUCCESS_URL" VALUE="${payment_data.success_url}"> 
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label>Failure CallBack URL</label>
                                        <INPUT  class="form-control" type="TEXT" NAME="FAILURE_URL" VALUE="${payment_data.failure_url}"> 
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label>Basket ID/Order ID</label>
                                        <INPUT class="form-control"  type="TEXT" NAME="BASKET_ID" VALUE="${payment_data.order_id}"> 
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label>Order Date</label>
                                        <INPUT  class="form-control" type="TEXT" NAME="ORDER_DATE" VALUE="${payment_data.todays_date}"> 
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Checkout URL</label>
                                <INPUT  class="form-control" type="TEXT" NAME="CHECKOUT_URL" VALUE="${payment_data.response_url}"> 
                            </div>
                            <div class="form-group">
                                <INPUT  class="btn btn-primary" TYPE="SUBMIT" value="PAY NOW"> 
                            </div>
    
                        </form> 
                    </div>
                </div>
    
                <script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n" crossorigin="anonymous"></script>
                <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>
                <script>
                    //$("#payment_form").submit();
                </script>
            </div>
        </body>
    </html>
    `;
}

function getBranchWiseAccount(accountSettings, userName, merchantId, securedKey, branchId){
    let response = {};
    let storeId = "";
    try{
        if (accountSettings && accountSettings.branchwise_payments) {
            const branchwiseSettings = accountSettings.branchwise_settings || {};
            if (branchId !== 0 && branchwiseSettings[branchId]) {
                userName = branchwiseSettings[branchId].user_name;
                merchantId = branchwiseSettings[branchId].merchant_id;
                storeId = branchwiseSettings[branchId].store_id;
                securedKey = branchwiseSettings[branchId].secure_key;
                // Now you have the account_id for the branch
            }
            response = {
                userName : userName,
                merchantId : merchantId,
                securedKey: securedKey,
                storeId: storeId
            };
            console.log(response);
            return response;
        } else {
            response = {
                userName : userName,
                merchantId : merchantId,
                securedKey: securedKey,
                storeId: storeId
            };
            return response;
        }
    } 
    catch (error){
        console.log("error "+error);
        response = { error : error };
        return response;
    }
}
function getCurrentDate() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}