image: node:18

pipelines:
  branches:
    master:
      - step:
          name: Deploy to Master
          caches:
            - node
          script:
            - npm ci
            - npm install -g serverless@4.14.4
            - cp .env.prod .env
            - serverless --version
            - serverless deploy --stage prod --verbose
    development:
      - step:
          name: Deploy to Development
          caches:
            - node
          script:
            - npm ci
            - npm install -g serverless@4.14.4
            - cp .env.dev .env
            - serverless --version
            - serverless deploy --stage dev --verbose
