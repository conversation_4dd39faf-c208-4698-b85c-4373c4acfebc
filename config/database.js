const mysql = require('mysql');
const dotenv = require('dotenv');
dotenv.config();

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
});

const superAdminDB = mysql.createPool({
  host: process.env.SUPER_ADMIN_HOST,
  user: process.env.SUPER_ADMIN_USER,
  password: process.env.SUPER_ADMIN_PASSWORD,
  database: process.env.SUPER_ADMIN_DATABASE,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

// Function to convert query execution to a promise
function queryPromise(pool, sql, params = []) {
  return new Promise((resolve, reject) => {
    pool.query(sql, params, (error, results) => {
      if (error) {
        reject(error);
      } else {
        resolve(results);
      }
    });
  });
}

module.exports = {
  pool,
  superAdminDB,
  queryPromise, // Exporting the promise-based query function
};
